import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:google_mlkit_commons/google_mlkit_commons.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_theme.dart';

class MLKitScanScreen extends StatefulWidget {
  final Function(String)? onBarcodeDetected;

  const MLKitScanScreen({super.key, this.onBarcodeDetected});

  @override
  State<MLKitScanScreen> createState() => _MLKitScanScreenState();
}

class _MLKitScanScreenState extends State<MLKitScanScreen> {
  CameraController? _cameraController;
  late BarcodeScanner _barcodeScanner;
  bool _isControllerInitialized = false;
  bool _isControllerDisposed = false;
  bool _isDetecting = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _barcodeScanner = BarcodeScanner();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      // B: Request Camera Permission
      final status = await Permission.camera.request();
      if (status != PermissionStatus.granted) {
        // B2-B3: Show Permission Dialog
        if (mounted) {
          _showPermissionDialog();
        }
        return;
      }

      // C: Open ML Kit Camera Screen
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        // Use back camera for better barcode scanning
        final camera = cameras.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.back,
          orElse: () => cameras.first,
        );

        _cameraController = CameraController(
          camera,
          ResolutionPreset.medium, // Medium resolution for better performance
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.yuv420, // Prefer YUV420 format
        );

        // C1: Initialize Camera Controller
        await _cameraController!.initialize();

        if (mounted) {
          setState(() => _isControllerInitialized = true);
          // C2: Start ML Kit Image Stream
          _startBarcodeDetection();
        }
      }
    } catch (e) {
      print('Camera initialization error: $e');
      if (mounted) {
        _showErrorDialog('Camera initialization failed: $e');
      }
    }
  }

  void _startBarcodeDetection() {
    if (_cameraController == null || !_cameraController!.value.isInitialized) return;

    _cameraController!.startImageStream((CameraImage image) async {
      await _processCameraImage(image);
    });
  }

  void _showErrorDialog(String message) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Camera Permission Required'),
        content: const Text('Please grant camera permission in settings to scan barcodes.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _processCameraImage(CameraImage image) async {
    if (_isDetecting || _isControllerDisposed || !mounted) return;

    _isDetecting = true;

    try {
      // Convert camera image to input image with robust format handling
      final inputImage = _convertToInputImage(image);
      if (inputImage == null) return;

      // Process the image
      final barcodes = await _barcodeScanner.processImage(inputImage);

      print('[MLKit] Processed image, found ${barcodes.length} barcodes');

      if (barcodes.isNotEmpty && mounted && !_isControllerDisposed) {
        final barcode = barcodes.first;
        final barcodeValue = barcode.displayValue ?? barcode.rawValue;

        if (barcodeValue != null) {
          print('[MLKit] Detected barcode: $barcodeValue (format: ${barcode.format})');

          // If we have a callback, use it (for scan screen)
          if (widget.onBarcodeDetected != null) {
            widget.onBarcodeDetected!(barcodeValue);
          } else {
            // Otherwise, stop camera and return via Navigator (for standalone use)
            await _stopCamera();
            if (mounted) {
              Navigator.of(context).pop(barcodeValue);
            }
          }
        }
      }
    } catch (e) {
      print('[MLKit] Error processing image: $e');
    } finally {
      _isDetecting = false;
    }
  }

  InputImage? _convertToInputImage(CameraImage image) {
    try {
      // Debug: Log image format details
      final rawFormat = image.format.raw;
      final formatGroup = image.format.group;

      // Log format details occasionally for debugging
      if (DateTime.now().millisecondsSinceEpoch % 200 == 0) {
        print('[MLKit] Camera image format - Raw: $rawFormat, Group: $formatGroup, Size: ${image.width}x${image.height}, Planes: ${image.planes.length}');
      }

      // Get image rotation
      final camera = _cameraController!.description;
      final rotation = InputImageRotationValue.fromRawValue(camera.sensorOrientation);
      if (rotation == null) {
        print('[MLKit] Unsupported rotation: ${camera.sensorOrientation}');
        return null;
      }

      // For Android, try to handle common formats more robustly
      InputImageFormat? format;

      // Try to map common Android formats
      switch (rawFormat) {
        case 17: // NV21
          format = InputImageFormat.nv21;
          break;
        case 875704438: // YUV_420_888
          format = InputImageFormat.yuv420;
          break;
        case 842094169: // YV12
          format = InputImageFormat.yuv420; // Map YV12 to YUV420
          break;
        case 1111970369: // BGRA8888 (iOS)
          format = InputImageFormat.bgra8888;
          break;
        default:
          // Try to get format from group
          switch (formatGroup) {
            case ImageFormatGroup.nv21:
              format = InputImageFormat.nv21;
              break;
            case ImageFormatGroup.yuv420:
              format = InputImageFormat.yuv420;
              break;
            case ImageFormatGroup.bgra8888:
              format = InputImageFormat.bgra8888;
              break;
            default:
              // Try the original method as fallback
              format = InputImageFormatValue.fromRawValue(rawFormat);
              break;
          }
      }

      if (format == null) {
        // Log unsupported format for debugging (but only occasionally to avoid spam)
        if (DateTime.now().millisecondsSinceEpoch % 100 == 0) {
          print('[MLKit] Unsupported format - Raw: $rawFormat, Group: $formatGroup');
        }
        return null;
      }

      // Ensure we have valid image planes
      if (image.planes.isEmpty) {
        print('[MLKit] No image planes available');
        return null;
      }

      // For multi-plane formats, we need to handle them differently
      Uint8List bytes;
      int bytesPerRow;

      if (image.planes.length == 1) {
        // Single plane format (like NV21)
        bytes = image.planes.first.bytes;
        bytesPerRow = image.planes.first.bytesPerRow;
      } else {
        // Multi-plane format (like YUV_420_888)
        // For YUV formats, we typically use the Y plane
        bytes = image.planes.first.bytes;
        bytesPerRow = image.planes.first.bytesPerRow;
      }

      if (bytes.isEmpty) {
        print('[MLKit] Empty image bytes');
        return null;
      }

      // Create input image metadata
      final inputImageMetadata = InputImageMetadata(
        size: Size(image.width.toDouble(), image.height.toDouble()),
        rotation: rotation,
        format: format,
        bytesPerRow: bytesPerRow,
      );

      // Create input image
      return InputImage.fromBytes(
        bytes: bytes,
        metadata: inputImageMetadata,
      );
    } catch (e) {
      // Log conversion errors occasionally for debugging
      if (DateTime.now().millisecondsSinceEpoch % 50 == 0) {
        print('[MLKit] Error converting image: $e');
      }
      return null;
    }
  }

  Future<void> _stopCamera() async {
    if (_cameraController != null && _isControllerInitialized && !_isControllerDisposed) {
      try {
        await _cameraController!.stopImageStream();
        print('[MLKit] Camera stream stopped');
      } catch (e) {
        print('[MLKit] Error stopping camera stream: $e');
      }
    }
  }

  @override
  void dispose() {
    _isControllerDisposed = true;
    _stopCamera();
    _cameraController?.dispose();
    _barcodeScanner.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Scan Barcode',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeCamera,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (!_isControllerInitialized || _isControllerDisposed || _cameraController == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Initializing camera...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // Camera preview
        Positioned.fill(
          child: CameraPreview(_cameraController!),
        ),
        
        // Scanning overlay
        Positioned.fill(
          child: CustomPaint(
            painter: ScannerOverlayPainter(),
          ),
        ),
        
        // Instructions
        Positioned(
          bottom: 100,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: const Text(
              'Point your camera at a barcode',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }
}

class ScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    const scanAreaSize = 250.0;
    final scanRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: scanAreaSize,
      height: scanAreaSize * 0.6,
    );

    // Draw the overlay background
    final overlayPaint = Paint()..color = Colors.black.withOpacity(0.6);
    canvas.drawRect(rect, overlayPaint);

    // Clear the scan area (create the cutout effect)
    final clearPaint = Paint()..blendMode = BlendMode.clear;
    canvas.drawRRect(
      RRect.fromRectAndRadius(scanRect, const Radius.circular(12)),
      clearPaint,
    );

    // Draw the scan frame
    canvas.drawRRect(
      RRect.fromRectAndRadius(scanRect, const Radius.circular(12)),
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );

    // Draw corner indicators
    final cornerLength = 20.0;
    final cornerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    // Top-left corner
    canvas.drawLine(
      Offset(scanRect.left, scanRect.top + cornerLength),
      Offset(scanRect.left, scanRect.top),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(scanRect.left, scanRect.top),
      Offset(scanRect.left + cornerLength, scanRect.top),
      cornerPaint,
    );

    // Top-right corner
    canvas.drawLine(
      Offset(scanRect.right - cornerLength, scanRect.top),
      Offset(scanRect.right, scanRect.top),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(scanRect.right, scanRect.top),
      Offset(scanRect.right, scanRect.top + cornerLength),
      cornerPaint,
    );

    // Bottom-left corner
    canvas.drawLine(
      Offset(scanRect.left, scanRect.bottom - cornerLength),
      Offset(scanRect.left, scanRect.bottom),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(scanRect.left, scanRect.bottom),
      Offset(scanRect.left + cornerLength, scanRect.bottom),
      cornerPaint,
    );

    // Bottom-right corner
    canvas.drawLine(
      Offset(scanRect.right - cornerLength, scanRect.bottom),
      Offset(scanRect.right, scanRect.bottom),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(scanRect.right, scanRect.bottom),
      Offset(scanRect.right, scanRect.bottom - cornerLength),
      cornerPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 
